from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify
from app.models import User, History, Commit
from app.database import db
import logging
log = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s',
                    datefmt='%d/%m/%Y %H:%M:%S')
main_bp = Blueprint('main', __name__)

@main_bp.route('/')
def index():
    """Chuyển hướng đến trang History."""
    return redirect(url_for('main.history'))

@main_bp.route('/users')
def users():
    """Display all users."""
    all_users = User.get_all()
    return render_template('users.html', title='Users', users=all_users)



@main_bp.route('/history')
def history():
    """Hiển thị lịch sử deploy với phân trang."""
    # L<PERSON>y các tham số từ query string
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    selected_project = request.args.get('project', '')
    selected_warehouse = request.args.get('warehouse', '')
    selected_environment = request.args.get('environment', '')
    search_query = request.args.get('search', '')

    # Lấy dữ liệu History với phân trang và bộ lọc
    data = History.get_all_data(
        page=page,
        per_page=per_page,
        project=selected_project if selected_project else None,
        warehouse=selected_warehouse if selected_warehouse else None,
        environment=selected_environment if selected_environment else None,
        search_query=search_query
    )

    # Tạo URL cơ sở cho phân trang (giữ nguyên các tham số lọc)
    base_url = url_for('main.history')
    query_params = request.args.copy()

    # Xóa tham số page để tạo URL cơ sở
    if 'page' in query_params:
        query_params.pop('page')

    # Tạo URL cơ sở với các tham số lọc
    if query_params:
        # Xây dựng query string thủ công để tránh lỗi với to_dict()
        query_string = '&'.join([f"{key}={value}" for key, value in query_params.items()])
        base_url = f"{base_url}?{query_string}&"
    else:
        base_url = f"{base_url}?"

    return render_template(
        'history.html',
        title='History',
        data=data,
        selected_project=selected_project,
        selected_warehouse=selected_warehouse,
        selected_environment=selected_environment,
        base_url=base_url
    )



@main_bp.route('/api/commits/<int:history_id>')
def get_commits(history_id):
    """API để lấy danh sách commit cho một bản ghi history"""
    try:
        # Kiểm tra xem history có tồn tại không
        if not History.query.get(history_id):
            return jsonify({
                'success': False,
                'error': 'History not found'
            }), 404

        # Lấy danh sách commit
        commits = Commit.get_commits_for_history_id(history_id)

        return jsonify({
            'success': True,
            'commits': commits
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@main_bp.route('/api/commits-not-in-prd')
def get_commits_not_in_prd():
    """API để lấy danh sách commit chưa lên PRD theo project và warehouse"""
    try:
        # Lấy tham số từ query string
        project = request.args.get('project', '')
        warehouse = request.args.get('warehouse', '')

        # Kiểm tra tham số
        if not project or not warehouse:
            return jsonify({
                'success': False,
                'error': 'Project và Warehouse là bắt buộc'
            }), 400

        # Lấy danh sách commit chưa lên PRD
        commits = Commit.get_commits_not_in_prd(project, warehouse)
        #log.info(f"Commits not in PRD: {commits}")

        # Nếu không có commit nào, trả về thông báo
        if not commits:
            return jsonify({
                'success': True,
                'message': f'Tất cả commit ID đã được đẩy lên PRD cho {project} - {warehouse}',
                'commits': []
            })

        return jsonify({
            'success': True,
            'commits': commits
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


