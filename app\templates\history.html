{% extends "base.html" %}

{% block title %}History{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/history.css') }}">
{% endblock %}

{% block content %}
<div class="bg-white p-6 rounded-lg shadow-md">
    <h1 class="text-3xl font-bold text-blue-600 mb-4"><PERSON><PERSON>ch sử Deploy</h1>

    <div class="mb-6">
        <a href="{{ url_for('main.index') }}" class="text-blue-600 hover:underline">
            Quay lại trang chủ
        </a>
    </div>

    <!-- Bộ lọc -->
    <div class="mb-6 bg-gray-100 p-4 rounded-lg">
        <h2 class="text-xl font-semibold text-gray-700 mb-3">Bộ lọc</h2>
        <form action="{{ url_for('main.history') }}" method="get" class="flex flex-wrap gap-4">
            <div class="w-full md:w-auto">
                <label for="project" class="block text-sm font-medium text-gray-700 mb-1">Project</label>
                <select id="project" name="project" class="w-full md:w-auto px-4 py-2 rounded border border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 h-10">
                    <option value="">Tất cả</option>
                    {% for project in data.projects %}
                    <option value="{{ project }}" {% if selected_project == project %}selected{% endif %}>{{ project }}</option>
                    {% endfor %}
                </select>
            </div>

            <div class="w-full md:w-auto">
                <label for="warehouse" class="block text-sm font-medium text-gray-700 mb-1">Warehouse</label>
                <select id="warehouse" name="warehouse" class="w-full md:w-auto px-4 py-2 rounded border border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 h-10">
                    <option value="">Tất cả</option>
                    {% for warehouse in data.warehouses %}
                    <option value="{{ warehouse }}" {% if selected_warehouse == warehouse %}selected{% endif %}>{{ warehouse }}</option>
                    {% endfor %}
                </select>
            </div>

            <div class="w-full md:w-auto">
                <label for="environment" class="block text-sm font-medium text-gray-700 mb-1">Environment</label>
                <select id="environment" name="environment" class="w-full md:w-auto px-4 py-2 rounded border border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 h-10">
                    <option value="">Tất cả</option>
                    {% for environment in data.environments %}
                    <option value="{{ environment }}" {% if selected_environment == environment %}selected{% endif %}>{{ environment }}</option>
                    {% endfor %}
                </select>
            </div>

            <div class="w-full md:w-auto">
                <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Tìm kiếm</label>
                <input type="text" id="search" name="search" placeholder="Tìm trong Branch, Message" value="{{ request.args.get('search', '') }}"
                       class="w-full md:w-80 px-4 py-2 rounded border border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 h-10">
            </div>

            <div class="w-full md:w-auto flex items-end">
                <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 h-10">
                    Lọc
                </button>
                <a href="{{ url_for('main.history') }}" class="ml-2 bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600 h-10 flex items-center">
                    Đặt lại
                </a>
                <button type="button" id="filterNotInPrdBtn" class="ml-2 bg-yellow-600 text-white px-4 py-2 rounded hover:bg-yellow-700 h-10 disabled:opacity-50 disabled:cursor-not-allowed">
                    Lọc Commit chưa lên PRD
                </button>
            </div>
        </form>
    </div>

    <!-- Hiển thị dữ liệu dạng bảng -->
    <div>
        {% if not data.records %}
            <div class="text-center py-8">
                <p class="text-gray-600">Không có dữ liệu.</p>
            </div>
        {% else %}
            <table class="bg-white border">
                <thead class="bg-gray-100">
                    <tr>
                        <th class="py-3 px-4 border-b font-semibold text-gray-700 text-center time-column">Time</th>
                        <th class="py-3 px-4 border-b font-semibold text-gray-700 text-center deploy-column">Deploy</th>
                        <th class="py-3 px-4 border-b font-semibold text-gray-700 text-center number-column">Number</th>
                        <th class="py-3 px-4 border-b font-semibold text-gray-700 text-center">Branch</th>
                        <th class="py-3 px-4 border-b font-semibold text-gray-700 text-center">Message</th>
                        <th class="py-3 px-4 border-b font-semibold text-gray-700 text-center">Action</th>
                    </tr>
                </thead>
                <tbody>
                    {% for record in data.records %}
                    <tr class="hover:bg-gray-50 {% if loop.index % 2 == 0 %}bg-gray-50{% endif %}"
                        data-id="{{ record.id }}"
                        data-last-branch="{{ record.last_branch }}"
                        data-commit-sha="{{ record.commit_sha }}"
                        data-file-name="{{ record.file_name }}"
                        data-error-log="{{ record.error_log or '' }}"
                    >
                        <td class="py-2 px-4 border-b text-center time-column">{{ record.request_date_time.strftime('%d/%m %H:%M') if record.request_date_time else 'N/A' }}</td>
                        <td class="py-2 px-4 border-b text-center deploy-column">
                            <div class="deploy-items">
                                <span class="deploy-item project-tag">
                                    {{ record.project }}
                                </span>
                                {% if record.warehouse == 'ALSW' %}
                                <span class="deploy-item warehouse-alsw">
                                    {{ record.warehouse }}
                                </span>
                                {% elif record.warehouse == 'ALSB' %}
                                <span class="deploy-item warehouse-alsb">
                                    {{ record.warehouse }}
                                </span>
                                {% elif record.warehouse == 'CLC' %}
                                <span class="deploy-item warehouse-clc">
                                    {{ record.warehouse }}
                                </span>
                                {% elif record.warehouse == 'ALSE' %}
                                <span class="deploy-item warehouse-alse">
                                    {{ record.warehouse }}
                                </span>
                                {% else %}
                                <span class="deploy-item warehouse-alsw">
                                    {{ record.warehouse }}
                                </span>
                                {% endif %}
                                {% if record.environment == 'STABLE' %}
                                    <span class="deploy-item env-stable">
                                        {{ record.environment }}
                                    </span>
                                {% elif record.environment == 'UAT2' %}
                                    <span class="deploy-item env-uat2">
                                        {{ record.environment }}
                                    </span>
                                {% elif record.environment == 'UAT3' %}
                                    <span class="deploy-item env-uat3">
                                        {{ record.environment }}
                                    </span>
                                {% elif record.environment == 'UAT4' %}
                                    <span class="deploy-item env-uat4">
                                        {{ record.environment }}
                                    </span>
                                {% elif record.environment == 'UAT5' %}
                                    <span class="deploy-item env-uat5">
                                        {{ record.environment }}
                                    </span>
                                {% else %}
                                    <span class="deploy-item env-other">
                                        {{ record.environment }}
                                    </span>
                                {% endif %}
                            </div>
                        </td>
                        <td class="py-2 px-4 border-b text-center number-column">{{ record.branch_number }}</td>
                        <td class="py-2 px-4 border-b text-center branch-column">{{ record.branch }}</td>
                        <td class="py-2 px-4 border-b text-center message-column">{{ record.message }}</td>
                        <td class="py-2 px-4 border-b text-center action-column">
                            <div class="flex items-center justify-center space-x-2">
                                <button type="button" class="action-btn view-btn" title="Xem">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                    </svg>
                                </button>
                                <button type="button" class="action-btn edit-btn" title="Sửa">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                    </svg>
                                </button>
                                <button type="button" class="action-btn delete-btn" title="Xóa">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>

            <!-- Phân trang -->
            {% if data.pagination and data.pagination.total_pages > 1 %}
            <div class="pagination-container mt-6 flex justify-center">
                <div class="flex items-center space-x-1">
                    <!-- Nút First (Trang đầu tiên) -->
                    {% if data.pagination.page > 1 %}
                    <a href="{{ base_url }}page=1" class="pagination-btn" title="Trang đầu tiên">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M15.707 5.293a1 1 0 010 1.414L12.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-5-5a1 1 0 010-1.414l5-5a1 1 0 011.414 0z M8.707 5.293a1 1 0 010 1.414L5.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-5-5a1 1 0 010-1.414l5-5a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                        <span class="sr-only">Trang đầu tiên</span>
                    </a>
                    {% else %}
                    <span class="pagination-btn opacity-50 cursor-not-allowed">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M15.707 5.293a1 1 0 010 1.414L12.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-5-5a1 1 0 010-1.414l5-5a1 1 0 011.414 0z M8.707 5.293a1 1 0 010 1.414L5.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-5-5a1 1 0 010-1.414l5-5a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                        <span class="sr-only">Trang đầu tiên</span>
                    </span>
                    {% endif %}

                    <!-- Nút Previous -->
                    {% if data.pagination.page > 1 %}
                    <a href="{{ base_url }}page={{ data.pagination.page - 1 }}" class="pagination-btn" title="Trang trước">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                        <span class="sr-only">Trang trước</span>
                    </a>
                    {% else %}
                    <span class="pagination-btn opacity-50 cursor-not-allowed">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                        <span class="sr-only">Trang trước</span>
                    </span>
                    {% endif %}

                    <!-- Các nút số trang -->
                    {% set start_page = [data.pagination.page - 2, 1]|max %}
                    {% set end_page = [start_page + 4, data.pagination.total_pages + 1]|min %}
                    {% set start_page = [end_page - 5, 1]|max %}

                    {% for p in range(start_page, end_page) %}
                        {% if p == data.pagination.page %}
                        <span class="pagination-btn pagination-active">{{ p }}</span>
                        {% else %}
                        <a href="{{ base_url }}page={{ p }}" class="pagination-btn">{{ p }}</a>
                        {% endif %}
                    {% endfor %}

                    <!-- Nút Next -->
                    {% if data.pagination.page < data.pagination.total_pages %}
                    <a href="{{ base_url }}page={{ data.pagination.page + 1 }}" class="pagination-btn" title="Trang sau">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        </svg>
                        <span class="sr-only">Trang sau</span>
                    </a>
                    {% else %}
                    <span class="pagination-btn opacity-50 cursor-not-allowed">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        </svg>
                    </span>
                    {% endif %}

                    <!-- Nút Last (Trang cuối cùng) -->
                    {% if data.pagination.page < data.pagination.total_pages %}
                    <a href="{{ base_url }}page={{ data.pagination.total_pages }}" class="pagination-btn" title="Trang cuối cùng">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M4.293 14.707a1 1 0 010-1.414L7.586 10 4.293 6.707a1 1 0 011.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z M11.293 14.707a1 1 0 010-1.414L14.586 10 11.293 6.707a1 1 0 011.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        </svg>
                        <span class="sr-only">Trang cuối cùng</span>
                    </a>
                    {% else %}
                    <span class="pagination-btn opacity-50 cursor-not-allowed">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M4.293 14.707a1 1 0 010-1.414L7.586 10 4.293 6.707a1 1 0 011.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z M11.293 14.707a1 1 0 010-1.414L14.586 10 11.293 6.707a1 1 0 011.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        </svg>
                        <span class="sr-only">Trang cuối cùng</span>
                    </span>
                    {% endif %}
                </div>
            </div>

            <!-- Thông tin phân trang và dropdown chọn số bản ghi -->
            <div class="flex justify-between items-center text-sm text-gray-600 mt-2 px-4">
                <div>
                    Hiển thị {{ data.records|length }} / {{ data.pagination.total_records }} bản ghi
                </div>
                <div class="flex items-center">
                    <label for="per-page-select" class="mr-2">Hiển thị:</label>
                    <select id="per-page-select" class="per-page-select">
                        <option value="5" {% if data.pagination.per_page == 5 %}selected{% endif %}>5</option>
                        <option value="10" {% if data.pagination.per_page == 10 %}selected{% endif %}>10</option>
                        <option value="20" {% if data.pagination.per_page == 20 %}selected{% endif %}>20</option>
                        <option value="50" {% if data.pagination.per_page == 50 %}selected{% endif %}>50</option>
                        <option value="100" {% if data.pagination.per_page == 100 %}selected{% endif %}>100</option>
                    </select>
                    <span class="ml-2">bản ghi mỗi trang</span>
                </div>
            </div>
            {% endif %}
        {% endif %}
    </div>

    <!-- Modal Dialog Chi tiết Deploy -->
    <div id="detailModal" class="modal">
    <div class="modal-content relative mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center border-b pb-3">
            <h3 class="text-xl font-semibold text-gray-700">Chi tiết Deploy</h3>
            <button type="button" id="closeModal" class="text-gray-400 hover:text-gray-500" title="Đóng">
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <div class="mt-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="bg-gray-50 p-3 rounded">
                    <h4 class="font-medium text-gray-700">Thông tin Deploy</h4>
                    <div class="mt-2 space-y-2">
                        <div class="flex flex-wrap gap-2 mb-2">
                            <span id="modal-project" class="deploy-item project-tag"></span>
                            <span id="modal-warehouse" class="deploy-item"></span>
                            <span id="modal-environment" class="deploy-item"></span>
                        </div>
                        <p><span class="font-medium">Branch Number:</span> <span id="modal-branch-number"></span></p>
                        <p><span class="font-medium">Thời gian:</span> <span id="modal-time"></span></p>
                    </div>
                </div>

                <div class="bg-gray-50 p-3 rounded">
                    <h4 class="font-medium text-gray-700">Thông tin Branch</h4>
                    <div class="mt-2 space-y-2">
                        <p><span class="font-medium">Branch:</span> <span id="modal-branch"></span></p>
                        <p><span class="font-medium">Last Branch:</span> <span id="modal-last-branch"></span></p>
                        <p><span class="font-medium">File Name:</span> <span id="modal-file-name"></span></p>
                    </div>
                </div>
            </div>

            <div class="mt-4 bg-gray-50 p-3 rounded">
                <h4 class="font-medium text-gray-700">Thông tin Commit</h4>
                <div class="mt-2 space-y-2">
                    <p><span class="font-medium">Message:</span></p>
                    <div class="message-container relative">
                        <div class="bg-gray-800 text-white p-3 rounded font-mono text-sm overflow-x-auto whitespace-pre-wrap" id="modal-message"></div>
                        <button type="button" id="copy-message-btn" class="copy-btn" title="Copy to clipboard">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                            </svg>
                        </button>
                    </div>

                    <!-- Bảng Commit SHA -->
                    <div class="mt-3">
                        <h5 class="font-medium text-gray-700 mb-2">Commit SHA</h5>
                        <div class="overflow-x-auto">
                            <table class="min-w-full bg-white border">
                                <thead class="bg-gray-100">
                                    <tr>
                                        <th class="py-2 px-3 border-b font-semibold text-gray-700 text-center w-12">STT</th>
                                        <th class="py-2 px-3 border-b font-semibold text-gray-700">SHA</th>
                                        <th class="py-2 px-3 border-b font-semibold text-gray-700 text-center w-16">UAT</th>
                                        <th class="py-2 px-3 border-b font-semibold text-gray-700 text-center w-16">PRD</th>
                                    </tr>
                                </thead>
                                <tbody id="commit-table-body">
                                    <!-- Dữ liệu sẽ được thêm vào bằng JavaScript -->
                                    <tr>
                                        <td colspan="4" class="py-2 px-3 border-b text-center text-gray-500">Đang tải dữ liệu...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-4 bg-gray-50 p-3 rounded" id="error-log-section">
                <h4 class="font-medium text-gray-700">Error Log</h4>
                <div class="mt-2">
                    <p id="modal-error-log" class="text-red-600"></p>
                </div>
            </div>
        </div>

        <div class="mt-6 flex justify-end">
            <button type="button" id="closeModalBtn" class="px-4 py-2 bg-gray-300 text-gray-800 rounded hover:bg-gray-400">
                Đóng
            </button>
        </div>
    </div>
</div>

    <!-- Modal Dialog Commit chưa lên PRD -->
    <div id="notInPrdModal" class="modal">
    <div class="modal-content relative mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center border-b pb-3">
            <h3 class="text-xl font-semibold text-gray-700">Commit chưa lên PRD</h3>
            <button type="button" id="closeNotInPrdModal" class="text-gray-400 hover:text-gray-500" title="Đóng">
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <div class="mt-4">
            <div class="bg-gray-50 p-3 rounded">
                <h4 class="font-medium text-gray-700">Thông tin</h4>
                <div class="mt-2 space-y-2">
                    <div class="flex flex-wrap gap-2 mb-2">
                        <span id="not-in-prd-project" class="deploy-item project-tag"></span>
                        <span id="not-in-prd-warehouse" class="deploy-item"></span>
                    </div>
                </div>
            </div>

            <div class="mt-4 bg-gray-50 p-3 rounded">
                <h4 class="font-medium text-gray-700">Thông tin Commit</h4>
                <div class="mt-2 space-y-2">
                    <p><span class="font-medium">Message:</span></p>
                    <div class="message-container relative">
                        <div class="bg-gray-800 text-white p-3 rounded font-mono text-sm overflow-x-auto whitespace-pre-wrap" id="not-in-prd-message"></div>
                        <button type="button" id="copy-not-in-prd-message-btn" class="copy-btn" title="Copy to clipboard">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                            </svg>
                        </button>
                    </div>

                    <!-- Bảng Commit SHA -->
                    <div class="mt-3">
                        <h5 class="font-medium text-gray-700 mb-2">Commit SHA</h5>
                        <div class="overflow-x-auto">
                            <table class="min-w-full bg-white border">
                                <thead class="bg-gray-100">
                                    <tr>
                                        <th class="py-2 px-3 border-b font-semibold text-gray-700 text-center w-12">STT</th>
                                        <th class="py-2 px-3 border-b font-semibold text-gray-700">SHA</th>
                                        <th class="py-2 px-3 border-b font-semibold text-gray-700 text-center w-16">UAT</th>
                                        <th class="py-2 px-3 border-b font-semibold text-gray-700 text-center w-16">PRD</th>
                                    </tr>
                                </thead>
                                <tbody id="not-in-prd-table-body">
                                    <!-- Dữ liệu sẽ được thêm vào bằng JavaScript -->
                                    <tr>
                                        <td colspan="4" class="py-2 px-3 border-b text-center text-gray-500">Đang tải dữ liệu...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-6 flex justify-end">
            <button type="button" id="closeNotInPrdModalBtn" class="px-4 py-2 bg-gray-300 text-gray-800 rounded hover:bg-gray-400">
                Đóng
            </button>
        </div>
    </div>
</div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/history.js') }}"></script>
{% endblock %}
